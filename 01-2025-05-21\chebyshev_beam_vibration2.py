import numpy as np
import matplotlib.pyplot as plt
from scipy.linalg import eig

def chebyshev_differentiation_matrix(N):
    """
    Chebyshev differentiation matrix D_N

    Parameters:
    -----------
    N : int
        Number of collocation points - 1

    Returns:
    --------
    D : ndarray, shape (N+1, N+1)
        Chebyshev differentiation matrix
    """
    # Collocation points
    j = np.arange(0, N+1)
    x = np.cos(np.pi * j / N)

    # Initialize D
    D = np.zeros((N+1, N+1))

    # Fill diagonal elements
    D[0, 0] = (2 * N**2 + 1) / 6
    D[N, N] = -D[0, 0]

    for j in range(1, N):
        D[j, j] = -x[j] / (2 * (1 - x[j]**2))

    # Fill off-diagonal elements
    c = np.ones(N+1)
    c[0] = c[N] = 2

    for i in range(N+1):
        for j in range(N+1):
            if i != j:
                D[i, j] = (c[i] / c[j]) * (-1)**(i+j) / (x[i] - x[j])

    return D

def axially_fg_beam_vibration(N, boundary_condition, material_params, taper_params, p=2):
    """
    Vibration analysis of axially functionally graded tapered Euler-Bernoulli beams
    using Chebyshev collocation method

    Parameters:
    -----------
    N : int
        Number of collocation points - 1
    boundary_condition : str
        Boundary condition type: 'CF', 'PP', 'CP', 'CC'
        (C: clamped, F: free, P: pinned)
    material_params : dict
        Material parameters: E0, EL, rho0, rhoL
    taper_params : dict
        Taper parameters: Ch, Cb
    p : float
        Volume fraction index for power-law function

    Returns:
    --------
    omega : ndarray
        Natural frequencies
    """
    # Collocation points
    j = np.arange(0, N+1)
    xi = np.cos(np.pi * j / N)

    # Material and geometric properties
    E0 = material_params['E0']
    EL = material_params['EL']
    rho0 = material_params['rho0']
    rhoL = material_params['rhoL']
    A0 = material_params['A0']
    I0 = material_params['I0']
    L = material_params['L']

    Ch = taper_params['Ch']
    Cb = taper_params['Cb']

    # Compute differentiation matrices
    D1 = chebyshev_differentiation_matrix(N)
    D2 = np.matmul(D1, D1)
    D3 = np.matmul(D1, D2)
    D4 = np.matmul(D1, D3)

    # Define bending rigidity S(ξ) and mass per unit length m(ξ)
    # Using power-law function as in equation (22)
    def S(xi):
        x = L * (xi + 1) / 2  # Transform from [-1, 1] to [0, L]
        E = E0 + (EL - E0) * (x/L)**p
        I = I0 * (1 - Cb * x/L) * (1 - Ch * x/L)**3
        return E * I

    def m(xi):
        x = L * (xi + 1) / 2  # Transform from [-1, 1] to [0, L]
        rho = rho0 + (rhoL - rho0) * (x/L)**p
        A = A0 * (1 - Cb * x/L) * (1 - Ch * x/L)
        return rho * A

    def dS_dxi(xi):
        # Numerical approximation of the derivative of S(ξ)
        h = 1e-6
        return (S(xi + h) - S(xi - h)) / (2 * h)

    def d2S_dxi2(xi):
        # Numerical approximation of the second derivative of S(ξ)
        h = 1e-6
        return (S(xi + h) - 2 * S(xi) + S(xi - h)) / h**2

    # Create diagonal matrices K1, K2, K3, M
    K1 = np.diag([S(xi_i) for xi_i in xi])
    K2 = np.diag([dS_dxi(xi_i) for xi_i in xi])
    K3 = np.diag([d2S_dxi2(xi_i) for xi_i in xi])
    M = np.diag([m(xi_i) for xi_i in xi])

    # Form the stiffness matrix
    K = np.matmul(K1, D4) + 2 * np.matmul(K2, D3) + np.matmul(K3, D2)

    # Apply boundary conditions
    # Replace rows corresponding to boundary conditions
    if boundary_condition == 'CF':  # Clamped-Free
        # Left end (clamped): W = 0, dW/dξ = 0
        K[0, :] = np.zeros(N+1)
        K[0, 0] = 1  # W = 0
        K[1, :] = D1[0, :]  # dW/dξ = 0

        # Right end (free): d²W/dξ² = 0, d/dξ(S(ξ)d²W/dξ²) = 0
        K[N-1, :] = D2[N, :]  # d²W/dξ² = 0
        K[N, :] = S(xi[N]) * D3[N, :] + dS_dxi(xi[N]) * D2[N, :]  # d/dξ(S(ξ)d²W/dξ²) = 0

        # Modify mass matrix
        M[0, :] = np.zeros(N+1)
        M[1, :] = np.zeros(N+1)
        M[N-1, :] = np.zeros(N+1)
        M[N, :] = np.zeros(N+1)

    elif boundary_condition == 'PP':  # Pinned-Pinned
        # Left end (pinned): W = 0, d²W/dξ² = 0
        K[0, :] = np.zeros(N+1)
        K[0, 0] = 1  # W = 0
        K[1, :] = D2[0, :]  # d²W/dξ² = 0

        # Right end (pinned): W = 0, d²W/dξ² = 0
        K[N-1, :] = np.zeros(N+1)
        K[N-1, N] = 1  # W = 0
        K[N, :] = D2[N, :]  # d²W/dξ² = 0

        # Modify mass matrix
        M[0, :] = np.zeros(N+1)
        M[1, :] = np.zeros(N+1)
        M[N-1, :] = np.zeros(N+1)
        M[N, :] = np.zeros(N+1)

    elif boundary_condition == 'CP':  # Clamped-Pinned
        # Left end (clamped): W = 0, dW/dξ = 0
        K[0, :] = np.zeros(N+1)
        K[0, 0] = 1  # W = 0
        K[1, :] = D1[0, :]  # dW/dξ = 0

        # Right end (pinned): W = 0, d²W/dξ² = 0
        K[N-1, :] = np.zeros(N+1)
        K[N-1, N] = 1  # W = 0
        K[N, :] = D2[N, :]  # d²W/dξ² = 0

        # Modify mass matrix
        M[0, :] = np.zeros(N+1)
        M[1, :] = np.zeros(N+1)
        M[N-1, :] = np.zeros(N+1)
        M[N, :] = np.zeros(N+1)

    elif boundary_condition == 'CC':  # Clamped-Clamped
        # Left end (clamped): W = 0, dW/dξ = 0
        K[0, :] = np.zeros(N+1)
        K[0, 0] = 1  # W = 0
        K[1, :] = D1[0, :]  # dW/dξ = 0

        # Right end (clamped): W = 0, dW/dξ = 0
        K[N-1, :] = np.zeros(N+1)
        K[N-1, N] = 1  # W = 0
        K[N, :] = D1[N, :]  # dW/dξ = 0

        # Modify mass matrix
        M[0, :] = np.zeros(N+1)
        M[1, :] = np.zeros(N+1)
        M[N-1, :] = np.zeros(N+1)
        M[N, :] = np.zeros(N+1)

    # Solve the generalized eigenvalue problem
    eigenvalues, eigenvectors = eig(K, M)

    # Filter out eigenvalues with significant imaginary parts
    valid_indices = np.where(np.abs(np.imag(eigenvalues)) < 1e-10)[0]
    eigenvalues = eigenvalues[valid_indices].real
    eigenvectors = eigenvectors[:, valid_indices]

    # Filter out negative eigenvalues
    valid_indices = np.where(eigenvalues > 0)[0]
    eigenvalues = eigenvalues[valid_indices]
    eigenvectors = eigenvectors[:, valid_indices]

    # Sort eigenvalues and eigenvectors
    idx = np.argsort(eigenvalues)
    eigenvalues = eigenvalues[idx]
    eigenvectors = eigenvectors[:, idx]

    # Calculate natural frequencies
    omega = np.sqrt(eigenvalues)

    return omega, eigenvectors, xi

def calculate_dimensionless_frequencies(omega, material_params):
    """
    Calculate dimensionless frequency parameter

    Parameters:
    -----------
    omega : ndarray
        Natural frequencies
    material_params : dict
        Material parameters

    Returns:
    --------
    lambda_i : ndarray
        Dimensionless frequency parameters
    """
    E0 = material_params['E0']
    rho0 = material_params['rho0']
    A0 = material_params['A0']
    I0 = material_params['I0']
    L = material_params['L']

    lambda_i = omega * L**2 * np.sqrt(rho0 * A0 / (E0 * I0))

    return lambda_i

def plot_mode_shapes(eigenvectors, xi, num_modes=3, bc='', save_fig=True):
    """
    Plot mode shapes

    Parameters:
    -----------
    eigenvectors : ndarray
        Eigenvectors (mode shapes)
    xi : ndarray
        Collocation points
    num_modes : int
        Number of modes to plot
    bc : str
        Boundary condition
    save_fig : bool
        Whether to save the figure to a file
    """
    plt.figure(figsize=(10, 6))

    for i in range(num_modes):
        # Normalize mode shape
        mode_shape = eigenvectors[:, i].real / np.max(np.abs(eigenvectors[:, i].real))
        plt.plot(xi, mode_shape, label=f'Mode {i+1}')

    plt.xlabel('ξ')
    plt.ylabel('Normalized displacement')
    plt.title(f'Mode shapes - {bc} boundary condition')
    plt.grid(True)
    plt.legend()

    if save_fig:
        plt.savefig(f'mode_shapes_{bc}.png')
    else:
        plt.show()

def main():
    # Number of collocation points
    N = 20

    # Material parameters (Zirconia-Aluminum)
    material_params = {
        'E0': 200e9,      # Young's modulus at x=0 (Pa)
        'EL': 70e9,       # Young's modulus at x=L (Pa)
        'rho0': 5700,     # Density at x=0 (kg/m^3)
        'rhoL': 2702,     # Density at x=L (kg/m^3)
        'A0': 4e-4,       # Cross-sectional area at x=0 (m^2)
        'I0': 1.33e-8,    # Area moment of inertia at x=0 (m^4)
        'L': 1.0          # Beam length (m)
    }

    # Taper parameters
    taper_params = {
        'Ch': 0.3,        # Height taper ratio
        'Cb': 0.0         # Width taper ratio
    }

    # Volume fraction index
    p = 2

    # Boundary conditions to analyze
    boundary_conditions = ['CF', 'PP', 'CP', 'CC']

    # Analyze each boundary condition
    for bc in boundary_conditions:
        print(f"\nBoundary condition: {bc}")

        # Calculate natural frequencies
        omega, eigenvectors, xi = axially_fg_beam_vibration(N, bc, material_params, taper_params, p)

        # Calculate dimensionless frequencies
        lambda_i = calculate_dimensionless_frequencies(omega, material_params)

        # Print first three natural frequencies
        print("Dimensionless frequencies:")
        for i in range(min(3, len(lambda_i))):
            print(f"λ{i+1} = {lambda_i[i]:.4f}")

        # Plot mode shapes
        plot_mode_shapes(eigenvectors, xi, num_modes=min(3, len(lambda_i)), bc=bc)

if __name__ == "__main__":
    main()
