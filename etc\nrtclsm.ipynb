{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyNrlPQygrTubYMDvnybvxvq"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": [], "metadata": {"id": "pX0qPygfu_q4"}}, {"cell_type": "code", "source": ["import numpy as np\n", "\n", "def compute_chebyshev_differentiation_matrix(n):\n", "    \"\"\"\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON> diferansi<PERSON>l matrisini hesaplar [[5]].\n", "    \"\"\"\n", "    c = np.zeros(n)\n", "    c[0] = 2\n", "    c[1:-1] = 1\n", "    c[-1] = 2\n", "    c = c * (-1)**np.arange(n)\n", "\n", "    X = np.tile(np.cos(np.pi * np.arange(n) / (n - 1)), (n, 1)).T\n", "    dX = X - X.T + np.eye(n)\n", "\n", "    D = (c[:, None] * (1 / dX).T).T / c[:, None]\n", "    D -= np.diag(np.sum(D, axis=1))\n", "    return D\n", "\n", "\n", "def chebyshev_collocation_method(rho, A, E, I, L, n, bc, material_graded_index=0, taper_ratios=(0, 0)):\n", "    \"\"\"\n", "    Chebyshev collocation method for free vibration analysis of axially FG Euler-<PERSON>lli beams.\n", "\n", "    Parameters:\n", "        rho: <PERSON><PERSON><PERSON><PERSON> (kg/m^3)\n", "        A: <PERSON><PERSON><PERSON> (m^2)\n", "        E: Elastisite modülü (Pa)\n", "        I: <PERSON><PERSON> moment<PERSON> (m^4)\n", "        L: <PERSON><PERSON><PERSON> (m)\n", "        n: <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "        bc: <PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON> ('cantilever', 'simply-supported', 'general')\n", "        material_graded_index: <PERSON><PERSON><PERSON> (p)\n", "        taper_ratios: <PERSON><PERSON> (Cb, Ch)\n", "\n", "    Returns:\n", "        omega: <PERSON><PERSON><PERSON>\n", "        lambdaL: Boy<PERSON>uz doğal frekanslar\n", "    \"\"\"\n", "    # Gauss-Chebyshev-Lobatto noktaları\n", "    xi = np.cos(np.pi * np.arange(n) / (n - 1))  # [-1, 1] aralığında noktalar\n", "\n", "    # Malzeme ve kesit özellikleri\n", "    Cb, Ch = taper_ratios\n", "    S = lambda xi: E * I * (1 + xi * Cb) * (1 - xi * Ch)**3\n", "    m = lambda xi: rho * A * (1 + xi * Cb) * (1 - xi * Ch)\n", "\n", "    # S(xi) ve m(xi) değ<PERSON><PERSON>ini hesapla\n", "    S_values = S(xi)\n", "    m_values = m(xi)\n", "\n", "    # <PERSON><PERSON><PERSON><PERSON><PERSON> diferansiyel matrisler\n", "    D = compute_chebyshev_differentiation_matrix(n)\n", "    D2 = np.dot(D, D)  # <PERSON><PERSON><PERSON> türev matrisi\n", "    D4 = np.dot(D2, D2)  # <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> türev matrisi\n", "\n", "    # Genelleştirilmiş özdeğer problemi\n", "    K1 = np.diag(S_values)  # <PERSON><PERSON><PERSON><PERSON> rijitliğinin köşegen matrisi\n", "    K2 = np.diag(np.gradient(S_values, xi))  # S'nin türevinin kö<PERSON>egen matrisi\n", "    K3 = np.diag(np.gradient(np.gradient(S_values, xi), xi))  # S'nin ikinci türevinin köşegen matrisi\n", "    M = np.diag(m_values)  # Kütle matrisinin köşegen matrisi\n", "\n", "    # <PERSON><PERSON><PERSON><PERSON><PERSON>: (K1*D4 + 2*K2*D3 + K3*D2)W = Ω²*M*W\n", "    A = K1 @ D4 + 2 * K2 @ D2 + K3 @ D2\n", "    B = M\n", "\n", "    # <PERSON><PERSON><PERSON><PERSON>r koşullarını uygula\n", "    A, B = apply_boundary_conditions(A, B, bc, D, D2)\n", "\n", "    # Özdeğer problemi çöz\n", "    eigenvalues, _ = np.linalg.eig(np.linalg.inv(B) @ A)\n", "    omega = np.sqrt(np.real(eigenvalues))\n", "    lambdaL = np.sqrt(omega) * (rho * A / (E * I))**(1/4)\n", "\n", "    return omega, lambdaL\n", "\n", "\n", "def apply_boundary_conditions(A, B, bc, D, D2):\n", "    \"\"\"\n", "    Sınır koşullarını matrise uygular.\n", "    \"\"\"\n", "    if bc == \"cantilever\":\n", "        # <PERSON><PERSON>i uç sabit\n", "        A = np.delete(A, [0, 1], axis=0)\n", "        A = np.delete(A, [0, 1], axis=1)\n", "        B = np.delete(B, [0, 1], axis=0)\n", "        B = np.delete(B, [0, 1], axis=1)\n", "    elif bc == \"simply-supported\":\n", "        # <PERSON>ki uçta basit mesnetli\n", "        A = np.delete(A, [0, -2], axis=0)\n", "        A = np.delete(A, [0, -2], axis=1)\n", "        B = np.delete(B, [0, -2], axis=0)\n", "        B = np.delete(B, [0, -2], axis=1)\n", "    elif bc == \"general\":\n", "        # <PERSON>er ç<PERSON><PERSON><PERSON> ve rotasyonel yaylar\n", "        A[0, 0] += 1  # Örnek bir yayın etkisi\n", "        A[1, 1] += 1\n", "        A[-2, -2] += 1\n", "        A[-1, -1] += 1\n", "\n", "    return A, B\n", "\n", "\n", "if __name__ == \"__main__\":\n", "    # Parametreler\n", "    rho = 5700  # <PERSON><PERSON><PERSON><PERSON> (kg/m^3)\n", "    A = 4e-4    # <PERSON><PERSON><PERSON> (m^2)\n", "    E = 200e9   # Elastisite modü<PERSON>ü (Pa)\n", "    I = 1.33e-8 # <PERSON><PERSON> momenti (m^4)\n", "    L = 1       # <PERSON><PERSON><PERSON> (m)\n", "    n = 20      # <PERSON><PERSON><PERSON><PERSON><PERSON>ı\n", "    bc = \"cantilever\"  # <PERSON><PERSON><PERSON><PERSON><PERSON> ko<PERSON>\n", "    material_graded_index = -3  # <PERSON><PERSON><PERSON> g<PERSON> in<PERSON>\n", "    taper_ratios = (0.2, 0.3)  # <PERSON><PERSON> oran<PERSON>ı (Cb, Ch)\n", "\n", "    # Doğal frekansları hesapla\n", "    omega, lambdaL = chebyshev_collocation_method(rho, A, E, I, L, n, bc, material_graded_index, taper_ratios)\n", "\n", "    # Sonuçları yazdır\n", "    print(\"Do<PERSON>al <PERSON>lar (omega):\", omega)\n", "    print(\"Boyutsuz Doğal Frekanslar (lambdaL):\", lambdaL)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "uBPYe5PftF1E", "executionInfo": {"status": "ok", "timestamp": 1744809169273, "user_tz": -180, "elapsed": 40, "user": {"displayName": "İBRAHİM KELES", "userId": "10960618757151053473"}}, "outputId": "b277a272-6c65-43e9-ae5e-837b1b717d6b"}, "execution_count": 12, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["<PERSON><PERSON><PERSON> (omega): [2.16624174e+05            nan 1.78975243e+04 1.78975243e+04\n", " 1.00264596e+04 1.00264596e+04 7.92447439e+03 7.29139892e+03\n", " 5.22026136e+03 4.76355706e+03 4.37279315e+03 3.09574961e+03\n", " 2.40648738e+03 1.52751072e+03 9.94915804e+02 5.11809628e+02\n", "            nan 1.75704631e+02]\n", "Boyutsuz Doğal Frekanslar (lambdaL): [[            nan             nan             nan  21082.53783355\n", "              nan  13503.87687207             nan  10324.94010476\n", "              nan   7780.22172633             nan   6075.96519904\n", "              nan   4408.23104783             nan   3342.94948213\n", "              nan             nan]\n", " [117133.80440948             nan  14859.87237938             nan\n", "   11688.82084713             nan   9639.36833552             nan\n", "    7266.90145356             nan   6331.24959283             nan\n", "    4674.69635511             nan   3516.03573685             nan\n", "              nan   1926.46645029]\n", " [            nan             nan  11354.65944598   7849.77672844\n", "              nan   9660.52819981             nan   7872.86938566\n", "              nan   6084.98094028             nan   4885.99263388\n", "              nan   3742.10616209             nan   3204.94243919\n", "              nan             nan]\n", " [ 98837.53674868             nan   9871.14595917             nan\n", "              nan   5294.61538074   6626.1280387              nan\n", "    5666.08386441             nan   5255.89946291             nan\n", "    4251.71556697             nan   3645.95561878             nan\n", "              nan   2285.38917339]\n", " [            nan             nan             nan   3299.33313246\n", "    9569.28412922             nan             nan   6028.49680488\n", "              nan   5302.74662131             nan   4529.00543985\n", "              nan   3722.89801775             nan   3433.3876436\n", "              nan             nan]\n", " [ 90913.86462219             nan  14623.11869353             nan\n", "              nan   8827.49799568             nan   6046.92617358\n", "    4486.55922605             nan   5081.10513837             nan\n", "    4455.55545601             nan   4021.63901373             nan\n", "              nan   2587.88577869]\n", " [            nan             nan             nan  13272.68129999\n", "    7229.83403692             nan   8352.21638297             nan\n", "              nan   4463.67246735             nan   4570.65093533\n", "              nan   4002.5820475              nan   3827.06967409\n", "              nan             nan]\n", " [ 87259.69541019             nan  14789.7354244              nan\n", "    7845.15642596             nan             nan   8006.93900064\n", "              nan   5241.61934511   4829.52604274             nan\n", "    4882.07649939             nan   4548.3265562              nan\n", "              nan   2949.86822498]\n", " [            nan             nan             nan  13704.54559892\n", "              nan   7527.70673108   6958.30911326             nan\n", "    6972.93500468             nan             nan   4487.20702578\n", "              nan   4447.47681801             nan   4365.86783916\n", "              nan             nan]\n", " [ 85814.55446376             nan  14932.85171684             nan\n", "    8965.4279073              nan   6275.48924777             nan\n", "              nan   7055.77173407             nan   4377.10733798\n", "    5304.58067475             nan   5236.97982459             nan\n", "              nan   3403.13704235]\n", " [            nan             nan             nan  13996.25273076\n", "              nan   8862.78219164             nan   6108.92554739\n", "    5890.83584945             nan   6710.96538451             nan\n", "              nan   4921.07818158             nan   5090.25847088\n", "              nan             nan]\n", " [ 85712.54718126             nan  15463.6092337              nan\n", "    9890.52049437             nan   8299.45606679             nan\n", "    6190.35706839             nan             nan   6448.04734597\n", "    1588.71110515             nan   6115.98567743             nan\n", "              nan   3998.06905885]\n", " [            nan             nan             nan  14753.9922922\n", "              nan  10127.33518635             nan   8521.43878486\n", "              nan   6538.44719573   4435.64748385             nan\n", "              nan   3677.16904268             nan   6113.16562409\n", "              nan             nan]\n", " [ 87452.09329525             nan  16989.07562471             nan\n", "   11662.41799471             nan  10409.30873608             nan\n", "    8835.24890764             nan   8461.8613239              nan\n", "    3836.91721224   4390.52954436   6888.42080678             nan\n", "              nan   4874.07319923]\n", " [            nan             nan             nan  17495.24162973\n", "              nan  13156.86395345             nan  11786.40177939\n", "              nan  10256.68522225             nan   8914.07420179\n", "              nan   5962.64423261             nan   7813.65509965\n", "              nan             nan]\n", " [ 96808.23603178             nan  21239.47921947             nan\n", "   15391.3447465              nan  14124.72906721             nan\n", "   12296.9305834              nan  12552.61141572             nan\n", "   10881.60588698             nan   9266.10434945             nan\n", "              nan   6629.24843331]\n", " [            nan             nan             nan  31171.67878987\n", "              nan  24890.15306066             nan  22829.31958344\n", "              nan  20274.64574689             nan  18600.36015806\n", "              nan  15841.16334203             nan  15453.20646848\n", "              nan             nan]\n", " [ 60502.63973856             nan             nan  23399.7971896\n", "              nan  19117.35249008             nan  17648.23413288\n", "              nan  15708.52121923             nan  14425.84073282\n", "              nan  12378.48517769             nan  12163.76357545\n", "              nan             nan]]\n"]}, {"output_type": "stream", "name": "stderr", "text": ["<ipython-input-12-983c756de1bb>:72: RuntimeWarning: invalid value encountered in sqrt\n", "  omega = np.sqrt(np.real(eigenvalues))\n", "<ipython-input-12-983c756de1bb>:73: RuntimeWarning: invalid value encountered in power\n", "  lambdaL = np.sqrt(omega) * (rho * A / (E * I))**(1/4)\n"]}]}]}