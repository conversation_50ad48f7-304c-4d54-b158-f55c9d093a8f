{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "authorship_tag": "ABX9TyM+rIizOvaeR0PpJT1IbSVN"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from scipy.linalg import eigh\n", "\n", "# Define material properties\n", "E0 = 70e9  # <PERSON>'s modulus at the root (Pa)\n", "E1 = 200e9 # <PERSON>'s modulus at the tip (Pa)\n", "rho0 = 2700 # Density at the root (kg/m^3)\n", "rho1 = 7850 # Density at the tip (kg/m^3)\n", "L = 1.0  # Length of the beam (m)\n", "A0 = 0.01 # Cross sectional area at the root (m^2)\n", "A1 = 0.005 # Cross sectional area at the tip (m^2)\n", "\n", "# Define the number of elements and nodes\n", "n_elements = 10\n", "n_nodes = n_elements + 1\n", "\n", "# Define element properties\n", "def E(x):\n", "  \"\"\"Young's modulus as a function of x.\"\"\"\n", "  return E0 + (E1 - E0) * (x / L)\n", "\n", "def rho(x):\n", "  \"\"\"Density as a function of x.\"\"\"\n", "  return rho0 + (rho1 - rho0) * (x / L)\n", "\n", "def A(x):\n", "  \"\"\"Cross sectional area as a function of x.\"\"\"\n", "  return A0 + (A1 - A0) * (x / L)\n", "\n", "# Assemble the global stiffness and mass matrices\n", "K = np.zeros((n_nodes, n_nodes))\n", "M = np.zeros((n_nodes, n_nodes))\n", "\n", "# Implement Finite Element Method (FEM)\n", "for i in range(n_elements):\n", "    # Calculate element stiffness and mass matrices\n", "    element_length = L / n_elements\n", "    x_center = (i + 0.5) * element_length\n", "    k_element = E(x_center) * A(x_center) / element_length\n", "    m_element = rho(x_center) * A(x_center) * element_length\n", "\n", "    # Assemble element matrices into global matrices\n", "    if i == 0:\n", "        K[i, i] += k_element\n", "        K[i, i + 1] -= k_element\n", "        K[i + 1, i] -= k_element\n", "        K[i + 1, i+1] += k_element\n", "\n", "        M[i,i] += m_element/2\n", "        M[i+1,i+1] += m_element/2\n", "    elif i < n_elements -1:\n", "        K[i,i] += k_element\n", "        K[i,i+1] -= k_element\n", "        K[i+1,i] -= k_element\n", "        K[i+1,i+1] += k_element\n", "        K[i,i-1] -= k_element\n", "        K[i-1,i] -= k_element\n", "        K[i-1,i-1] += k_element\n", "\n", "        M[i,i] += m_element/2\n", "        M[i+1,i+1] += m_element/2\n", "\n", "    elif i == n_elements -1 :\n", "        K[i, i] += k_element\n", "        K[i, i - 1] -= k_element\n", "        K[i - 1, i] -= k_element\n", "        K[i - 1, i-1] += k_element\n", "\n", "        M[i,i] += m_element/2\n", "        M[i-1,i-1] += m_element/2\n", "\n", "# Fix one end of the beam by removing the first row and column from K and M\n", "K_fixed = K[1:, 1:]\n", "M_fixed = M[1:, 1:]\n", "\n", "# Solve the generalized eigenvalue problem\n", "eigenvalues, eigenvectors = eigh(K_fixed, M_fixed)\n", "\n", "# Extract natural frequencies\n", "omega = np.sqrt(eigenvalues)\n", "frequencies = omega / (2 * np.pi)\n", "\n", "# Print or plot results\n", "print('Natural frequencies (Hz):', frequencies)\n", "\n", "# Plotting frequencies\n", "plt.figure(figsize=(10,6))\n", "plt.plot(frequencies, marker='o', linestyle='-')\n", "plt.xlabel('Mode number')\n", "plt.ylabel('Natural Frequency (Hz)')\n", "plt.title('Natural Frequencies of the Functionally Graded Beam')\n", "plt.grid(True)\n", "plt.show()\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 349}, "id": "KwYe-wm276SU", "executionInfo": {"status": "error", "timestamp": 1744560960366, "user_tz": -180, "elapsed": 71, "user": {"displayName": "İBRAHİM KELES", "userId": "10960618757151053473"}}, "outputId": "b09dbf26-e77b-49ab-f488-9a2273ccd38e"}, "execution_count": null, "outputs": [{"output_type": "error", "ename": "LinAlgError", "evalue": "The leading minor of order 10 of B is not positive definite. The factorization of B could not be completed and no eigenvalues or eigenvectors were computed.", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mLinAlgError\u001b[0m                               <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-9-4bb90b4006c4>\u001b[0m in \u001b[0;36m<cell line: 0>\u001b[0;34m()\u001b[0m\n\u001b[1;32m     76\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     77\u001b[0m \u001b[0;31m# Solve the generalized eigenvalue problem\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 78\u001b[0;31m \u001b[0meigenvalues\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0meigenvectors\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0meigh\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mK_fixed\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mM_fixed\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     79\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     80\u001b[0m \u001b[0;31m# Extract natural frequencies\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/scipy/linalg/_decomp.py\u001b[0m in \u001b[0;36meigh\u001b[0;34m(a, b, lower, eigvals_only, overwrite_a, overwrite_b, type, check_finite, subset_by_index, subset_by_value, driver)\u001b[0m\n\u001b[1;32m    590\u001b[0m                               f'{drv.typecode + pfx + driver}')\n\u001b[1;32m    591\u001b[0m         \u001b[0;32melif\u001b[0m \u001b[0minfo\u001b[0m \u001b[0;34m>\u001b[0m \u001b[0mn\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 592\u001b[0;31m             raise LinAlgError(f'The leading minor of order {info-n} of B is not '\n\u001b[0m\u001b[1;32m    593\u001b[0m                               \u001b[0;34m'positive definite. The factorization of B '\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    594\u001b[0m                               \u001b[0;34m'could not be completed and no eigenvalues '\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mLinAlgError\u001b[0m: The leading minor of order 10 of B is not positive definite. The factorization of B could not be completed and no eigenvalues or eigenvectors were computed."]}]}, {"cell_type": "markdown", "source": ["# <PERSON><PERSON>ü<PERSON>"], "metadata": {"id": "t3RIFwR_wAFn"}}, {"cell_type": "markdown", "source": ["# <PERSON><PERSON>ü<PERSON>"], "metadata": {"id": "7dTTkFCqwA0D"}}, {"cell_type": "code", "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "from scipy.linalg import eigh\n", "\n", "# Fix one end of the beam\n", "K_fixed = K[1:, 1:]\n", "M_fixed = M[1:, 1:]\n", "\n", "# Regularize mass matrix if needed\n", "epsilon = 1e-6\n", "M_fixed += epsilon * np.eye(M_fixed.shape[0])\n", "\n", "# Solve generalized eigenvalue problem\n", "try:\n", "    eigenvalues, eigenvectors = eigh(K_fixed, M_fixed)\n", "except np.linalg.LinAlgError as e:\n", "    print(\"Error:\", e)\n", "    print(\"Mass matrix might not be positive definite.\")\n", "else:\n", "    # Extract natural frequencies\n", "    omega = np.sqrt(eigenvalues)\n", "    frequencies = omega / (2 * np.pi)\n", "\n", "    # Print results\n", "    print('Natural frequencies (Hz):', frequencies)\n", "\n", "    # Plot frequencies\n", "    plt.figure(figsize=(10,6))\n", "    plt.plot(frequencies, marker='o', linestyle='-')\n", "    plt.xlabel('Mode number')\n", "    plt.ylabel('Natural Frequency (Hz)')\n", "    plt.title('Natural Frequencies of the Functionally Graded Beam')\n", "    plt.grid(True)\n", "    plt.show()\n"], "metadata": {"id": "nIT_6jtX8IDv", "executionInfo": {"status": "ok", "timestamp": 1744561017225, "user_tz": -180, "elapsed": 271, "user": {"displayName": "İBRAHİM KELES", "userId": "10960618757151053473"}}, "outputId": "93600472-c6a0-4db3-84d7-31c7adef8cb5", "colab": {"base_uri": "https://localhost:8080/", "height": 651}}, "execution_count": null, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Natural frequencies (Hz): [           nan            nan     0.          5468.34682121\n", " 10135.70988081 13482.15308735 15746.34486016 17925.45132919\n", " 19861.1630824  21118.06382645]\n"]}, {"output_type": "stream", "name": "stderr", "text": ["<ipython-input-10-b8f893d2b51f>:21: RuntimeWarning: invalid value encountered in sqrt\n", "  omega = np.sqrt(eigenvalues)\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 1000x600 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}]}