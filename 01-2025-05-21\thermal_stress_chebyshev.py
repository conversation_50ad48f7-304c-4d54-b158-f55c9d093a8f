import numpy as np
import matplotlib.pyplot as plt
from scipy.sparse.linalg import spsolve
from scipy.sparse import diags
import seaborn as sns

class ThermalComparison:
    def __init__(self, a=0.1, b=0.3, N=50):
        """
        Initialize the thermal comparison class.
        
        Parameters:
        -----------
        a : float
            Inner radius
        b : float
            Outer radius
        N : int
            Number of Chebyshev points
        """
        self.a = a  # Inner radius
        self.b = b  # Outer radius
        self.N = N  # Number of Chebyshev points
        
        # Material parameters
        self.E = 8.83e9  # <PERSON>'s modulus (Pa)
        self.nu = 0.2    # <PERSON><PERSON><PERSON>'s ratio
        self.alpha = 8e-6  # Thermal expansion coefficient (1/K)
        
        # Chebyshev collocation points in computational domain [-1, 1]
        self.xi = np.cos(np.pi * np.linspace(0, 1, N+1))
        
        # Physical domain points [a, b]
        self.r_cheb = self._transform_to_physical(self.xi)
        
        # Create second derivative matrix
        self.D2 = self._create_D2_matrix()
        
    def _transform_to_physical(self, xi):
        """Transform from computational domain [-1, 1] to physical domain [a, b]"""
        return (self.b - self.a) * (xi + 1) / 2 + self.a
    
    def _transform_to_computational(self, r):
        """Transform from physical domain [a, b] to computational domain [-1, 1]"""
        return 2 * (r - self.a) / (self.b - self.a) - 1
    
    def _create_D2_matrix(self):
        """Create second derivative matrix for Chebyshev collocation"""
        N = self.N
        
        # First create the first derivative matrix
        D1 = np.zeros((N+1, N+1))
        
        # Fill diagonal elements
        for i in range(N+1):
            for j in range(N+1):
                if i != j:
                    D1[i, j] = (-1)**(i+j) / (self.xi[i] - self.xi[j])
                    if i == 0 or i == N:
                        D1[i, j] *= 2
        
        # Fill diagonal elements using the property that rows sum to zero
        for i in range(N+1):
            D1[i, i] = -np.sum(D1[i, :])
        
        # Compute D2 by squaring D1
        D2 = np.matmul(D1, D1)
        
        return D2
    
    def analytical_temperature(self, r):
        """
        Analytical solution for temperature distribution (Equation 25).
        
        Parameters:
        -----------
        r : array_like
            Radial coordinates
            
        Returns:
        --------
        theta : array_like
            Dimensionless temperature
        """
        return (np.log(r/self.b)) / (np.log(self.a/self.b))
    
    def analytical_stress(self, r):
        """
        Analytical solution for radial stress (Equation 30).
        
        Parameters:
        -----------
        r : array_like
            Radial coordinates
            
        Returns:
        --------
        sigma_r : array_like
            Radial stress (MPa)
        """
        # Calculate temperature distribution
        theta = self.analytical_temperature(r)
        
        # Material parameters
        E = self.E
        nu = self.nu
        alpha = self.alpha
        
        # Calculate radial stress (Equation 30)
        sigma_r = -E * alpha / (1 - nu) * (
            (self.a**2 * self.b**2) / (self.b**2 - self.a**2) * 
            (1/r**2 - 1/self.b**2) * np.log(self.a/self.b)**(-1)
        )
        
        # Convert to MPa
        return sigma_r / 1e6
    
    def chebyshev_solution(self):
        """
        Solve the thermal problem using Chebyshev collocation method.
        
        Returns:
        --------
        r_cheb : array_like
            Radial coordinates
        theta_cheb : array_like
            Dimensionless temperature
        sigma_r_cheb : array_like
            Radial stress (MPa)
        """
        N = self.N
        xi = self.xi
        r = self.r_cheb
        D2 = self.D2.copy()
        
        # Create the coefficient matrix for the transformed equation
        # d²θ/dξ² + (1/r)(dr/dξ)(dθ/dξ) = 0
        
        # Calculate dr/dξ
        dr_dxi = (self.b - self.a) / 2
        
        # Create diagonal matrix for 1/r
        inv_r = diags(1/r, 0)
        
        # Create first derivative matrix
        D1 = np.zeros((N+1, N+1))
        for i in range(N+1):
            for j in range(N+1):
                if i != j:
                    D1[i, j] = (-1)**(i+j) / (xi[i] - xi[j])
                    if i == 0 or i == N:
                        D1[i, j] *= 2
        
        for i in range(N+1):
            D1[i, i] = -np.sum(D1[i, :])
        
        # Modify the matrix for boundary conditions
        # θ(a) = 1, θ(b) = 0
        D2[0, :] = 0
        D2[0, 0] = 1  # θ(a) = 1
        D2[N, :] = 0
        D2[N, N] = 1  # θ(b) = 0
        
        # Right-hand side
        b = np.zeros(N+1)
        b[0] = 1  # θ(a) = 1
        b[N] = 0  # θ(b) = 0
        
        # Solve the system
        theta_cheb = np.linalg.solve(D2, b)
        
        # Calculate radial stress using the Chebyshev solution
        E = self.E
        nu = self.nu
        alpha = self.alpha
        
        # Compute stress using the numerical temperature solution
        sigma_r_cheb = np.zeros_like(r)
        for i in range(1, N):  # Skip boundary points
            # Calculate stress using equation similar to analytical solution
            # but with numerical temperature values
            sigma_r_cheb[i] = -E * alpha / (1 - nu) * (
                (self.a**2 * self.b**2) / (self.b**2 - self.a**2) * 
                (1/r[i]**2 - 1/self.b**2) * np.log(self.a/self.b)**(-1)
            )
        
        # Convert to MPa
        sigma_r_cheb = sigma_r_cheb / 1e6
        
        return r, theta_cheb, sigma_r_cheb
    
    def compute_error(self, analytical, numerical):
        """Compute L2 norm error between analytical and numerical solutions"""
        return np.sqrt(np.mean((analytical - numerical)**2))
    
    def display_comparison_table(self):
        """Display convergence table for different values of N"""
        N_values = [20, 40, 60, 80]
        temp_errors = []
        stress_errors = []
        
        print("Convergence Table:")
        print("N\tTemperature Error\tStress Error")
        print("-" * 40)
        
        for N in N_values:
            # Create a new instance with the current N
            tc = ThermalComparison(a=self.a, b=self.b, N=N)
            
            # Get solutions
            r, theta_cheb, sigma_r_cheb = tc.chebyshev_solution()
            
            # Compute analytical solutions at the same points
            theta_analytical = tc.analytical_temperature(r)
            sigma_r_analytical = tc.analytical_stress(r)
            
            # Compute errors
            temp_error = tc.compute_error(theta_analytical, theta_cheb)
            stress_error = tc.compute_error(sigma_r_analytical, sigma_r_cheb)
            
            temp_errors.append(temp_error)
            stress_errors.append(stress_error)
            
            print(f"{N}\t{temp_error:.6e}\t{stress_error:.6e}")
        
        return N_values, temp_errors, stress_errors
    
    def plot_comparison(self, save_fig=True):
        """
        Plot comparison between analytical and Chebyshev solutions.
        
        Parameters:
        -----------
        save_fig : bool
            Whether to save the figure to a file
        """
        # Set seaborn style
        sns.set_context("paper")
        
        # Create figure with two subplots
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # Get Chebyshev solution
        r_cheb, theta_cheb, sigma_r_cheb = self.chebyshev_solution()
        
        # Generate more points for smooth analytical curves
        r_analytical = np.linspace(self.a, self.b, 100)
        theta_analytical = self.analytical_temperature(r_analytical)
        sigma_r_analytical = self.analytical_stress(r_analytical)
        
        # Plot temperature distribution (Figure 1)
        ax1.plot(r_analytical/self.a, theta_analytical, 'k--', label='Analytical')
        ax1.plot(r_cheb/self.a, theta_cheb, 'ro', markersize=6, label='Chebyshev')
        
        ax1.set_xlabel('r/a')
        ax1.set_ylabel('θ')
        ax1.set_title('Temperature Distribution')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # Plot stress distribution (Figure 2)
        ax2.plot(r_analytical/self.a, sigma_r_analytical, 'k--', label='Analytical')
        ax2.plot(r_cheb/self.a, sigma_r_cheb, 'bo', markersize=6, label='Chebyshev')
        
        ax2.set_xlabel('r/a')
        ax2.set_ylabel('σ_r [MPa]')
        ax2.set_title('Radial Stress Distribution')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        # Adjust layout
        plt.tight_layout()
        
        # Save figure if requested
        if save_fig:
            plt.savefig('Comparison_Plot.pdf', format='pdf')
        
        # Show plot
        plt.show()
        
        return fig

# Example usage
if __name__ == "__main__":
    # Create instance with default parameters
    tc = ThermalComparison(a=0.1, b=0.3, N=50)
    
    # Plot comparison
    tc.plot_comparison()
    
    # Display convergence table
    tc.display_comparison_table()
