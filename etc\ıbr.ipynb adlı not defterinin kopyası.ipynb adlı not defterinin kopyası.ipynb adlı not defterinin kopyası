{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [{"file_id": "1s0oFYd05NIyY30HLJ2aRjBpCNUtnijwR", "timestamp": 1744557426948}], "authorship_tag": "ABX9TyN1P3ECnD9sr71Ei5uYBLBF"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "code", "source": ["!pip install fenics"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "N6StdlT42c47", "executionInfo": {"status": "ok", "timestamp": 1744559534208, "user_tz": -180, "elapsed": 5346, "user": {"displayName": "İBRAHİM KELES", "userId": "10960618757151053473"}}, "outputId": "f7134b90-185b-4b40-f5c6-952b32f6a33f"}, "execution_count": 15, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting fenics\n", "  Downloading fenics-2019.1.0-py3-none-any.whl.metadata (1.5 kB)\n", "Collecting fenics-ffc<2019.2,>=2019.1.0 (from fenics)\n", "  Downloading fenics_ffc-2019.1.0.post0-py3-none-any.whl.metadata (1.6 kB)\n", "Collecting fenics-fiat<2019.2,>=2019.1.0 (from fenics)\n", "  Downloading fenics_fiat-2019.1.0-py3-none-any.whl.metadata (420 bytes)\n", "Collecting fenics-ufl<2019.2,>=2019.1.0 (from fenics)\n", "  Downloading fenics_ufl-2019.1.0-py3-none-any.whl.metadata (1.1 kB)\n", "Collecting fenics-dijitso<2019.2,>=2019.1.0 (from fenics)\n", "  Downloading fenics_dijitso-2019.1.0-py3-none-any.whl.metadata (1.2 kB)\n", "Requirement already satisfied: numpy in /usr/local/lib/python3.11/dist-packages (from fenics-dijitso<2019.2,>=2019.1.0->fenics) (2.0.2)\n", "Requirement already satisfied: sympy in /usr/local/lib/python3.11/dist-packages (from fenics-fiat<2019.2,>=2019.1.0->fenics) (1.13.1)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy->fenics-fiat<2019.2,>=2019.1.0->fenics) (1.3.0)\n", "Downloading fenics-2019.1.0-py3-none-any.whl (1.5 kB)\n", "Downloading fenics_dijitso-2019.1.0-py3-none-any.whl (46 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m46.7/46.7 kB\u001b[0m \u001b[31m2.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading fenics_ffc-2019.1.0.post0-py3-none-any.whl (362 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m363.0/363.0 kB\u001b[0m \u001b[31m8.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading fenics_fiat-2019.1.0-py3-none-any.whl (112 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m112.5/112.5 kB\u001b[0m \u001b[31m7.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading fenics_ufl-2019.1.0-py3-none-any.whl (282 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m282.7/282.7 kB\u001b[0m \u001b[31m17.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: fenics-ufl, fenics-dijitso, fenics-fiat, fenics-ffc, fenics\n", "Successfully installed fenics-2019.1.0 fenics-dijitso-2019.1.0 fenics-ffc-2019.1.0.post0 fenics-fiat-2019.1.0 fenics-ufl-2019.1.0\n"]}]}, {"cell_type": "code", "source": ["import numpy as np\n", "from scipy.linalg import eig\n", "from scipy.sparse.linalg import eigsh\n", "import matplotlib.pyplot as plt\n", "\n", "# Beam parameters\n", "L = 1.0         # Length [m]\n", "b0 = 0.1        # Width at x=0 [m]\n", "h0 = 0.05       # Height at x=0 [m]\n", "E0 = 70e9       # <PERSON>'s modulus at x=0 [Pa]\n", "rho0 = 2700     # Density at x=0 [kg/m³]\n", "\n", "# Tapering parameters\n", "beta_b = 0.5    # Width tapering ratio\n", "beta_h = 0.3    # Height tapering ratio\n", "\n", "# FGM power law exponent\n", "p = 1.0\n", "\n", "# Discretization\n", "N = 100         # Number of elements\n", "x = np.linspace(0, L, N+1)\n", "dx = x[1] - x[0]\n", "\n", "# Material and geometric properties\n", "b = b0*(1 - beta_b*x/L)\n", "h = h0*(1 - beta_h*x/L)\n", "A = b*h\n", "I = b*h**3/12\n", "E = E0*(1 - x/L)**p\n", "rho = rho0*(1 - x/L)**p\n", "\n", "# Finite difference matrices (4th order)\n", "K = np.zeros((N+1, N+1))\n", "M = np.zeros((N+1, N+1))\n", "\n", "for i in range(2, N-1):\n", "    # 4th derivative finite difference approximation\n", "    K[i,i-2] += E[i]*I[i]/dx**4\n", "    K[i,i-1] += -4*E[i]*I[i]/dx**4\n", "    K[i,i]   += 6*E[i]*I[i]/dx**4\n", "    K[i,i+1] += -4*E[i]*I[i]/dx**4\n", "    K[i,i+2] += E[i]*I[i]/dx**4\n", "\n", "    # Mass matrix (lumped)\n", "    M[i,i] = rho[i]*A[i]\n", "\n", "# Apply clamped-clamped boundary conditions\n", "K = K[2:N-1, 2:N-1]\n", "M = M[2:N-1, 2:N-1]\n", "\n", "# Solve eigenvalue problem\n", "eigenvalues, eigenvectors = eig(K, M)\n", "omega = np.sqrt(np.real(eigenvalues))\n", "frequencies = omega/(2*np.pi)\n", "\n", "# Sort frequencies\n", "idx = np.argsort(frequencies)\n", "frequencies = frequencies[idx]\n", "eigenvectors = eigenvectors[:,idx]\n", "\n", "# Print first 5 frequencies\n", "print(\"First 5 natural frequencies (Hz):\")\n", "print(frequencies[:5])\n", "\n", "# Plot first 3 mode shapes\n", "plt.figure(figsize=(10,6))\n", "for i in range(3):\n", "    mode = np.zeros(N+1)\n", "    mode[2:N-1] = np.real(eigenvectors[:,i])\n", "    plt.plot(x, mode/max(abs(mode)), label=f'Mode {i+1}: {frequencies[i]:.2f} Hz')\n", "\n", "plt.title('Mode Shapes of Functionally Graded Tapered Beam')\n", "plt.xlabel('Beam length [m]')\n", "plt.ylabel('Normalized displacement')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 599}, "id": "gM1I6Fgm2iCj", "executionInfo": {"status": "ok", "timestamp": 1744559551680, "user_tz": -180, "elapsed": 1562, "user": {"displayName": "İBRAHİM KELES", "userId": "10960618757151053473"}}, "outputId": "da058eb1-3af8-4e8a-84bd-9c76ee6810ec"}, "execution_count": 16, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["First 5 natural frequencies (Hz):\n", "[ 225.87036069  621.60860554 1217.42536858 2010.85242383 3001.3453305 ]\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 1000x600 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}]}, {"cell_type": "code", "source": ["from scipy.integrate import solve_bvp\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# Beam parameters (same as before)\n", "\n", "def beam_ode(x, y, p):\n", "    # y[0] = w, y[1] = w', y[2] = M, y[3] = V\n", "    omega = p[0]\n", "    b = b0*(1 - beta_b*x/L)\n", "    h = h0*(1 - beta_h*x/L)\n", "    I = b*h**3/12\n", "    A = b*h\n", "    E = E0*(1 - x/L)**p\n", "    rho = rho0*(1 - x/L)**p\n", "\n", "    return np.vstack((\n", "        y[1],\n", "        -y[2]/(E*I),\n", "        y[3],\n", "        rho*A*omega**2*y[0]\n", "    ))\n", "\n", "def bc(ya, yb, p):\n", "    # Clamped-clamped boundary conditions\n", "    return np.array([\n", "        ya[0],  # w(0) = 0\n", "        ya[1],  # w'(0) = 0\n", "        yb[0],  # w(L) = 0\n", "        yb[1]   # w'(L) = 0\n", "    ])\n", "\n", "# Initial guess\n", "x = np.linspace(0, L, 100)\n", "y_guess = np.zeros((4, x.size))\n", "omega_guess = 100  # Initial frequency guess\n", "\n", "# Solve for modes\n", "frequencies = []\n", "for mode in range(1, 4):\n", "    sol = solve_bvp(\n", "        lambda x,y,p: beam_ode(x,y,p),\n", "        bc,\n", "        x,\n", "        y_guess,\n", "        p=[omega_guess],\n", "        tol=1e-6\n", "    )\n", "    frequencies.append(sol.p[0]/(2*np.pi))\n", "    plt.plot(sol.x, sol.y[0], label=f'Mode {mode}: {frequencies[-1]:.2f} Hz')\n", "    omega_guess = sol.p[0] * 1.5  # Update guess for next mode\n", "\n", "plt.title('Mode Shapes')\n", "plt.xlabel('Beam length [m]')\n", "plt.ylabel('Normalized displacement')\n", "plt.legend()\n", "plt.grid()\n", "plt.show()\n", "\n", "print(\"Natural frequencies (Hz):\", frequencies)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 367}, "id": "w6hoCWTz2qGF", "executionInfo": {"status": "error", "timestamp": 1744559585265, "user_tz": -180, "elapsed": 51, "user": {"displayName": "İBRAHİM KELES", "userId": "10960618757151053473"}}, "outputId": "7e73499d-ee9e-4c5e-d0f1-211c4294ce17"}, "execution_count": 17, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["<ipython-input-17-05d5e9656648>:19: RuntimeWarning: invalid value encountered in divide\n", "  -y[2]/(E*I),\n"]}, {"output_type": "error", "ename": "ValueError", "evalue": "`bc` return is expected to have shape (5,), but actually has (4,).", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-17-05d5e9656648>\u001b[0m in \u001b[0;36m<cell line: 0>\u001b[0;34m()\u001b[0m\n\u001b[1;32m     39\u001b[0m \u001b[0mfrequencies\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m[\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     40\u001b[0m \u001b[0;32mfor\u001b[0m \u001b[0mmode\u001b[0m \u001b[0;32min\u001b[0m \u001b[0mrange\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;36m4\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 41\u001b[0;31m     sol = solve_bvp(\n\u001b[0m\u001b[1;32m     42\u001b[0m         \u001b[0;32mlambda\u001b[0m \u001b[0mx\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0my\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mp\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mbeam_ode\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mx\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0my\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mp\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     43\u001b[0m         \u001b[0mbc\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/scipy/integrate/_bvp.py\u001b[0m in \u001b[0;36msolve_bvp\u001b[0;34m(fun, bc, x, y, p, S, fun_jac, bc_jac, tol, max_nodes, verbose, bc_tol)\u001b[0m\n\u001b[1;32m   1068\u001b[0m     \u001b[0mbc_res\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mbc_wrapped\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0my\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0my\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m-\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mp\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1069\u001b[0m     \u001b[0;32mif\u001b[0m \u001b[0mbc_res\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mshape\u001b[0m \u001b[0;34m!=\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0mn\u001b[0m \u001b[0;34m+\u001b[0m \u001b[0mk\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1070\u001b[0;31m         raise ValueError(f\"`bc` return is expected to have shape {(n + k,)}, \"\n\u001b[0m\u001b[1;32m   1071\u001b[0m                          f\"but actually has {bc_res.shape}.\")\n\u001b[1;32m   1072\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mValueError\u001b[0m: `bc` return is expected to have shape (5,), but actually has (4,)."]}]}, {"cell_type": "code", "source": ["import numpy as np\n", "from scipy.integrate import solve_bvp\n", "import matplotlib.pyplot as plt\n", "\n", "# Beam parameters\n", "L = 1.0         # Length [m]\n", "b0 = 0.1        # Width at x=0 [m]\n", "h0 = 0.05       # Height at x=0 [m]\n", "E0 = 70e9       # <PERSON>'s modulus at x=0 [Pa]\n", "rho0 = 2700     # Density at x=0 [kg/m³]\n", "\n", "# Tapering parameters\n", "beta_b = 0.5    # Width tapering ratio\n", "beta_h = 0.3    # Height tapering ratio\n", "\n", "# FGM power law exponent\n", "p = 1.0\n", "\n", "def beam_ode(x, y, p):\n", "    \"\"\"System of ODEs for the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> beam vibration problem\"\"\"\n", "    omega = p[0]\n", "\n", "    # Geometric and material properties at position x\n", "    b = b0*(1 - beta_b*x/L)\n", "    h = h0*(1 - beta_h*x/L)\n", "    I = b*h**3/12\n", "    A = b*h\n", "    E = E0*(1 - x/L)**p\n", "    rho = rho0*(1 - x/L)**p\n", "\n", "    # Avoid division by zero\n", "    EI = E*I\n", "    EI[EI == 0] = np.finfo(float).eps\n", "\n", "    return np.vstack((\n", "        y[1],           # dw/dx = θ\n", "        y[2]/EI,        # dθ/dx = M/EI\n", "        y[3],           # dM/dx = V\n", "        -rho*A*omega**2*y[0]  # dV/dx = -ρAω²w\n", "    ))\n", "\n", "def bc(ya, yb, p):\n", "    \"\"\"Boundary conditions for clamped-clamped beam\"\"\"\n", "    return np.array([\n", "        ya[0],  # w(0) = 0\n", "        ya[1],  # θ(0) = 0\n", "        yb[0],  # w(L) = 0\n", "        yb[1]   # θ(L) = 0\n", "    ])\n", "\n", "# Initial mesh points\n", "x = np.linspace(0, L, 100)\n", "\n", "# Initial guess (sinusoidal shape)\n", "y_guess = np.zeros((4, x.size))\n", "y_guess[0] = np.sin(np.pi*x/L)  # Displacement\n", "y_guess[1] = np.pi/L*np.cos(np.pi*x/L)  # Slope\n", "\n", "# Initial frequency guess (based on simple beam theory)\n", "omega_guess = (np.pi/L)**2 * np.sqrt(E0*I[0]/(rho0*A[0]))\n", "\n", "# Solve for the first 3 modes\n", "frequencies = []\n", "mode_shapes = []\n", "\n", "for mode in range(1, 4):\n", "    # Adjust initial guess for higher modes\n", "    y_guess[0] = np.sin(mode*np.pi*x/L)\n", "    y_guess[1] = mode*np.pi/L*np.cos(mode*np.pi*x/L)\n", "    omega_guess = (mode*np.pi/L)**2 * np.sqrt(E0*I[0]/(rho0*A[0]))\n", "\n", "    sol = solve_bvp(\n", "        lambda x,y,p: beam_ode(x,y,p),\n", "        bc,\n", "        x,\n", "        y_guess,\n", "        p=[omega_guess],\n", "        tol=1e-6,\n", "        max_nodes=1000\n", "    )\n", "\n", "    if sol.success:\n", "        frequencies.append(sol.p[0]/(2*np.pi))\n", "        mode_shapes.append(sol.sol(x))\n", "        print(f\"Mode {mode}: {frequencies[-1]:.2f} Hz\")\n", "    else:\n", "        print(f\"Failed to converge for mode {mode}\")\n", "\n", "# Plot mode shapes\n", "plt.figure(figsize=(10, 6))\n", "for i, (freq, shape) in enumerate(zip(frequencies, mode_shapes)):\n", "    plt.plot(x, shape[0]/np.max(np.abs(shape[0])),\n", "             label=f'Mode {i+1}: {freq:.2f} Hz')\n", "\n", "plt.title('Normalized Mode Shapes of Functionally Graded Tapered Beam')\n", "plt.xlabel('Beam length [m]')\n", "plt.ylabel('Normalized displacement')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 332}, "id": "2k15frzj3Clc", "executionInfo": {"status": "error", "timestamp": 1744559715980, "user_tz": -180, "elapsed": 8, "user": {"displayName": "İBRAHİM KELES", "userId": "10960618757151053473"}}, "outputId": "35ae27b2-685d-44f0-991d-e269a282d916"}, "execution_count": 20, "outputs": [{"output_type": "error", "ename": "ValueError", "evalue": "`bc` return is expected to have shape (5,), but actually has (4,).", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-20-225851edaab7>\u001b[0m in \u001b[0;36m<cell line: 0>\u001b[0;34m()\u001b[0m\n\u001b[1;32m     70\u001b[0m     \u001b[0momega_guess\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0mmode\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpi\u001b[0m\u001b[0;34m/\u001b[0m\u001b[0mL\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m**\u001b[0m\u001b[0;36m2\u001b[0m \u001b[0;34m*\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msqrt\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mE0\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0mI\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m/\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mrho0\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0mA\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     71\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 72\u001b[0;31m     sol = solve_bvp(\n\u001b[0m\u001b[1;32m     73\u001b[0m         \u001b[0;32mlambda\u001b[0m \u001b[0mx\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0my\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mp\u001b[0m\u001b[0;34m:\u001b[0m \u001b[0mbeam_ode\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mx\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0my\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0mp\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     74\u001b[0m         \u001b[0mbc\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/scipy/integrate/_bvp.py\u001b[0m in \u001b[0;36msolve_bvp\u001b[0;34m(fun, bc, x, y, p, S, fun_jac, bc_jac, tol, max_nodes, verbose, bc_tol)\u001b[0m\n\u001b[1;32m   1068\u001b[0m     \u001b[0mbc_res\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mbc_wrapped\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0my\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0my\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m-\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mp\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1069\u001b[0m     \u001b[0;32mif\u001b[0m \u001b[0mbc_res\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mshape\u001b[0m \u001b[0;34m!=\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0mn\u001b[0m \u001b[0;34m+\u001b[0m \u001b[0mk\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1070\u001b[0;31m         raise ValueError(f\"`bc` return is expected to have shape {(n + k,)}, \"\n\u001b[0m\u001b[1;32m   1071\u001b[0m                          f\"but actually has {bc_res.shape}.\")\n\u001b[1;32m   1072\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mValueError\u001b[0m: `bc` return is expected to have shape (5,), but actually has (4,)."]}]}, {"cell_type": "code", "source": ["import numpy as np\n", "from scipy.integrate import solve_bvp\n", "import matplotlib.pyplot as plt\n", "\n", "# Beam parameters\n", "L = 1.0         # Length [m]\n", "b0 = 0.1        # Width at x=0 [m]\n", "h0 = 0.05       # Height at x=0 [m]\n", "E0 = 70e9       # <PERSON>'s modulus at x=0 [Pa]\n", "rho0 = 2700     # Density at x=0 [kg/m³]\n", "\n", "# Tapering parameters\n", "beta_b = 0.5    # Width tapering ratio\n", "beta_h = 0.3    # Height tapering ratio\n", "\n", "# FGM power law exponent\n", "p = 1.0\n", "\n", "def properties(x):\n", "    \"\"\"Calculate geometric and material properties at position x\"\"\"\n", "    b = b0*(1 - beta_b*x/L)\n", "    h = h0*(1 - beta_h*x/L)\n", "    I = b*h**3/12\n", "    A = b*h\n", "    E = E0*(1 - x/L)**p\n", "    rho = rho0*(1 - x/L)**p\n", "    return E, I, A, rho\n", "\n", "def beam_ode(x, y, p):\n", "    \"\"\"System of ODEs for the <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> beam vibration problem\"\"\"\n", "    omega = p[0]\n", "    E, I, A, rho = properties(x)\n", "\n", "    # Avoid division by zero\n", "    EI = E*I\n", "    EI[EI == 0] = np.finfo(float).eps\n", "\n", "    return np.vstack((\n", "        y[1],           # dw/dx = θ\n", "        y[2]/EI,        # dθ/dx = M/EI\n", "        y[3],           # dM/dx = V\n", "        -rho*A*omega**2*y[0]  # dV/dx = -ρAω²w\n", "    ))\n", "\n", "def bc(ya, yb, p):\n", "    \"\"\"Boundary conditions for clamped-clamped beam\"\"\"\n", "    return np.array([\n", "        ya[0],  # w(0) = 0\n", "        ya[1],  # θ(0) = 0\n", "        yb[0],  # w(L) = 0\n", "        yb[1]   # θ(L) = 0\n", "    ])\n", "\n", "# Initial mesh points\n", "x = np.linspace(0, L, 100)\n", "\n", "# Calculate properties at x=0 for initial guess\n", "E, I, A, rho = properties(0)\n", "omega_guess = (np.pi/L)**2 * np.sqrt(E*I/(rho*A))\n", "\n", "# Initial guess (sinusoidal shape)\n", "y_guess = np.zeros((4, x.size))\n", "y_guess[0] = np.sin(np.pi*x/L)  # Displacement\n", "y_guess[1] = np.pi/L*np.cos(np.pi*x/L)  # Slope\n", "\n", "# Solve for the first 3 modes\n", "frequencies = []\n", "mode_shapes = []\n", "\n", "for mode in range(1, 4):\n", "    # Adjust initial guess for higher modes\n", "    n = mode\n", "    y_guess[0] = np.sin(n*np.pi*x/L)\n", "    y_guess[1] = n*np.pi/L*np.cos(n*np.pi*x/L)\n", "    omega_guess = (n*np.pi/L)**2 * np.sqrt(E*I/(rho*A))\n", "\n", "    sol = solve_bvp(\n", "        beam_ode,\n", "        bc,\n", "        x,\n", "        y_guess,\n", "        p=[omega_guess],\n", "        tol=1e-6,\n", "        max_nodes=10000\n", "    )\n", "\n", "    if sol.success:\n", "        frequencies.append(sol.p[0]/(2*np.pi))\n", "        mode_shapes.append(sol.sol(x))\n", "        print(f\"Mode {mode}: {frequencies[-1]:.2f} Hz\")\n", "    else:\n", "        print(f\"Failed to converge for mode {mode}\")\n", "\n", "# Plot mode shapes\n", "plt.figure(figsize=(10, 6))\n", "for i, (freq, shape) in enumerate(zip(frequencies[:3], mode_shapes[:3])):\n", "    plt.plot(x, shape[0]/np.max(np.abs(shape[0])),\n", "             label=f'Mode {i+1}: {freq:.2f} Hz')\n", "\n", "plt.title('Normalized Mode Shapes of Functionally Graded Tapered Beam (Clamped-Clamped)')\n", "plt.xlabel('Beam length [m]')\n", "plt.ylabel('Normalized displacement')\n", "plt.legend()\n", "plt.grid(True)\n", "plt.show()"], "metadata": {"id": "X4H0S2ug3kW8", "executionInfo": {"status": "error", "timestamp": 1744559822933, "user_tz": -180, "elapsed": 52, "user": {"displayName": "İBRAHİM KELES", "userId": "10960618757151053473"}}, "outputId": "d5d7d9e6-1f90-4e91-ccc0-d1f506d9e258", "colab": {"base_uri": "https://localhost:8080/", "height": 332}}, "execution_count": 21, "outputs": [{"output_type": "error", "ename": "ValueError", "evalue": "`bc` return is expected to have shape (5,), but actually has (4,).", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-21-1a08c44a6565>\u001b[0m in \u001b[0;36m<cell line: 0>\u001b[0;34m()\u001b[0m\n\u001b[1;32m     75\u001b[0m     \u001b[0momega_guess\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0mn\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mpi\u001b[0m\u001b[0;34m/\u001b[0m\u001b[0mL\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m**\u001b[0m\u001b[0;36m2\u001b[0m \u001b[0;34m*\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0msqrt\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mE\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0mI\u001b[0m\u001b[0;34m/\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mrho\u001b[0m\u001b[0;34m*\u001b[0m\u001b[0mA\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     76\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 77\u001b[0;31m     sol = solve_bvp(\n\u001b[0m\u001b[1;32m     78\u001b[0m         \u001b[0mbeam_ode\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     79\u001b[0m         \u001b[0mbc\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/scipy/integrate/_bvp.py\u001b[0m in \u001b[0;36msolve_bvp\u001b[0;34m(fun, bc, x, y, p, S, fun_jac, bc_jac, tol, max_nodes, verbose, bc_tol)\u001b[0m\n\u001b[1;32m   1068\u001b[0m     \u001b[0mbc_res\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mbc_wrapped\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0my\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;36m0\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0my\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m-\u001b[0m\u001b[0;36m1\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mp\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1069\u001b[0m     \u001b[0;32mif\u001b[0m \u001b[0mbc_res\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mshape\u001b[0m \u001b[0;34m!=\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0mn\u001b[0m \u001b[0;34m+\u001b[0m \u001b[0mk\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1070\u001b[0;31m         raise ValueError(f\"`bc` return is expected to have shape {(n + k,)}, \"\n\u001b[0m\u001b[1;32m   1071\u001b[0m                          f\"but actually has {bc_res.shape}.\")\n\u001b[1;32m   1072\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mValueError\u001b[0m: `bc` return is expected to have shape (5,), but actually has (4,)."]}]}, {"cell_type": "code", "source": ["def bc(ya, yb, p):\n", "    \"\"\"Boundary conditions for simply supported beam\"\"\"\n", "    return np.array([\n", "        ya[0],  # w(0) = 0\n", "        yb[0],  # w(L) = 0\n", "        ya[2],  # M(0) = 0 (EI d²w/dx² = 0)\n", "        yb[2]   # M(L) = 0 (EI d²w/dx² = 0)\n", "    ])"], "metadata": {"id": "1nB1Z9P-3IkI", "executionInfo": {"status": "ok", "timestamp": 1744559708233, "user_tz": -180, "elapsed": 37, "user": {"displayName": "İBRAHİM KELES", "userId": "10960618757151053473"}}}, "execution_count": 19, "outputs": []}]}