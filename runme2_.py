import numpy as np
import matplotlib.pyplot as plt
from scipy.special import jv, yv
from scipy.optimize import root_scalar, fsolve
from scipy.linalg import eig
import warnings
warnings.filterwarnings('ignore')

class HollowCylinderThermal:
    """
    Hollow cylinder heat conduction problem çözümü:
    - Analytical solution (eigenvalue expansion)
    - Chebyshev collocation method
    """
    
    def __init__(self, a=1.0, b=1.5, kappa=50.0, T_initial=1150.0, T_boundary=650.0):
        """
        Parameters:
        -----------
        a : float, inner radius
        b : float, outer radius  
        kappa : float, thermal diffusivity
        T_initial : float, initial temperature
        T_boundary : float, boundary temperature (both inner and outer)
        """
        self.a = a  
        self.b = b
        self.kappa = kappa
        self.T_initial = T_initial
        self.T_boundary = T_boundary
        
        # Debug flags
        self.debug = True
        self.eigenvalues = []
        self.coefficients = []
        
    def debug_print(self, message):
        """Debug output"""
        if self.debug:
            print(f"[DEBUG] {message}")
    
    def find_eigenvalues_improved(self, n_roots=20):
        """
        Geliştirilmiş eigenvalue hesaplama
        Hollow cylinder için: J₀(λa)Y₀(λb) - J₀(λb)Y₀(λa) = 0
        """
        eigenvalues = []
        
        def characteristic_equation(lam):
            """Characteristic equation for eigenvalues"""
            try:
                term1 = jv(0, lam * self.a) * yv(0, lam * self.b)
                term2 = jv(0, lam * self.b) * yv(0, lam * self.a)
                return term1 - term2
            except:
                return np.inf
        
        # Daha sistematik root finding
        lam_start = 0.1
        for i in range(n_roots):
            # Search for sign changes
            lam_left = lam_start
            step = 0.1
            
            # Find bracket where function changes sign
            while True:
                lam_right = lam_left + step
                try:
                    f_left = characteristic_equation(lam_left)
                    f_right = characteristic_equation(lam_right)
                    
                    if f_left * f_right < 0:  # Sign change found
                        break
                    lam_left = lam_right
                    
                    if lam_left > 100:  # Prevent infinite loop
                        break
                except:
                    lam_left = lam_right
                    continue
            
            # Use root finding
            try:
                sol = root_scalar(characteristic_equation, 
                                bracket=[lam_left, lam_right], 
                                method='brentq')
                if sol.converged and abs(sol.root) > 1e-10:
                    eigenvalues.append(sol.root)
                    lam_start = sol.root + 0.1
                    self.debug_print(f"Eigenvalue {i+1}: λ = {sol.root:.6f}")
            except:
                # Fallback approximation
                lam_approx = (lam_left + lam_right) / 2
                eigenvalues.append(lam_approx)
                lam_start = lam_approx + 0.1
                self.debug_print(f"Eigenvalue {i+1} (approx): λ = {lam_approx:.6f}")
        
        self.eigenvalues = eigenvalues
        return eigenvalues
    
    def calculate_coefficients(self, eigenvalues):
        """
        Analytical solution için katsayıları hesapla
        """
        coefficients = []
        
        for lam in eigenvalues:
            try:
                # Normalization integral hesabı
                r_vals = np.linspace(self.a, self.b, 1000)
                
                # Eigenfunction: J₀(λr)Y₀(λb) - J₀(λb)Y₀(λr)
                def eigenfunction(r):
                    return (jv(0, lam * r) * yv(0, lam * self.b) - 
                            jv(0, lam * self.b) * yv(0, lam * r))
                
                # Normalization integral
                norm_integral = 0
                for r in r_vals:
                    phi_r = eigenfunction(r)
                    norm_integral += phi_r**2 * r * (self.b - self.a) / len(r_vals)
                
                # Coefficient hesabı
                initial_integral = 0
                for r in r_vals:
                    phi_r = eigenfunction(r)
                    # Initial condition: T(r,0) = T_initial
                    initial_integral += (self.T_initial - self.T_boundary) * phi_r * r * (self.b - self.a) / len(r_vals)
                
                if abs(norm_integral) > 1e-10:
                    coeff = initial_integral / norm_integral
                    coefficients.append(coeff)
                    self.debug_print(f"Coefficient for λ={lam:.4f}: A = {coeff:.6f}")
                else:
                    coefficients.append(0.0)
                    self.debug_print(f"Coefficient for λ={lam:.4f}: A = 0 (norm_integral too small)")
                    
            except Exception as e:
                coefficients.append(0.0)
                self.debug_print(f"Error calculating coefficient for λ={lam:.4f}: {str(e)}")
        
        self.coefficients = coefficients
        return coefficients
    
    def analytical_solution(self, r, t):
        """
        Gerçek analytical solution
        """
        if len(self.eigenvalues) == 0:
            self.find_eigenvalues_improved()
        
        if len(self.coefficients) == 0:
            self.calculate_coefficients(self.eigenvalues)
        
        # Steady state solution
        T_steady = np.ones_like(r) * self.T_boundary
        
        if t == 0:
            return np.ones_like(r) * self.T_initial
        
        # Transient solution
        T_transient = np.zeros_like(r)
        
        for i, (lam, coeff) in enumerate(zip(self.eigenvalues, self.coefficients)):
            try:
                # Eigenfunction
                phi_r = (jv(0, lam * r) * yv(0, lam * self.b) - 
                        jv(0, lam * self.b) * yv(0, lam * r))
                
                # Time decay
                time_decay = np.exp(-self.kappa * lam**2 * t)
                
                # Add contribution
                contribution = coeff * phi_r * time_decay
                T_transient += contribution
                
                # Debug for first few terms
                if i < 3:
                    self.debug_print(f"Term {i+1}: λ={lam:.4f}, A={coeff:.4f}, "
                                   f"time_decay={time_decay:.6f}, max_contrib={np.max(np.abs(contribution)):.4f}")
                    
            except Exception as e:
                self.debug_print(f"Error in term {i+1}: {str(e)}")
                continue
        
        return T_steady + T_transient
    
    def chebyshev_collocation(self, r, t, N=20):
        """
        Chebyshev collocation method implementation
        """
        self.debug_print(f"Chebyshev collocation with N={N} points")
        
        # Transform to [-1, 1] domain
        def transform_to_cheb(r_physical):
            return 2 * (r_physical - self.a) / (self.b - self.a) - 1
        
        def transform_from_cheb(xi):
            return self.a + (self.b - self.a) * (xi + 1) / 2
        
        # Chebyshev points
        xi = np.cos(np.pi * np.arange(N+1) / N)
        r_cheb = transform_from_cheb(xi)
        
        # Differentiation matrix
        D = self.chebyshev_diff_matrix(N)
        
        # Transform derivatives (chain rule)
        D_scaled = D * 2 / (self.b - self.a)
        D2_scaled = D_scaled @ D_scaled
        
        # Setup system for eigenvalue problem
        # For heat equation: ∂T/∂t = κ∇²T
        # In cylindrical coordinates: ∂T/∂t = κ(∂²T/∂r² + (1/r)∂T/∂r)
        
        # Mass matrix (identity for interior points)
        M = np.eye(N+1)
        
        # Stiffness matrix
        K = np.zeros((N+1, N+1))
        
        for i in range(1, N):  # Interior points
            K[i, :] = self.kappa * (D2_scaled[i, :] + D_scaled[i, :] / r_cheb[i])
        
        # Boundary conditions: T(a,t) = T(b,t) = T_boundary
        K[0, :] = 0
        K[0, 0] = 1
        K[N, :] = 0
        K[N, N] = 1
        
        M[0, 0] = 1
        M[N, N] = 1
        
        # Solve eigenvalue problem
        try:
            eigenvals, eigenvecs = eig(K, M)
            
            # Sort eigenvalues
            idx = np.argsort(np.real(eigenvals))
            eigenvals = eigenvals[idx]
            eigenvecs = eigenvecs[:, idx]
            
            self.debug_print(f"Chebyshev eigenvalues (first 5): {np.real(eigenvals[:5])}")
            
            # Initial condition projection
            T_initial_vec = np.ones(N+1) * self.T_initial
            T_initial_vec[0] = self.T_boundary  # Boundary conditions
            T_initial_vec[N] = self.T_boundary
            
            # Project initial condition onto eigenvectors
            coeffs_cheb = []
            for i in range(N+1):
                coeff = np.dot(T_initial_vec, eigenvecs[:, i]) / np.dot(eigenvecs[:, i], eigenvecs[:, i])
                coeffs_cheb.append(coeff)
            
            # Reconstruct solution at time t
            T_cheb = np.zeros(N+1)
            for i in range(N+1):
                if np.real(eigenvals[i]) > -1e10:  # Avoid numerical issues
                    T_cheb += coeffs_cheb[i] * eigenvecs[:, i] * np.exp(np.real(eigenvals[i]) * t)
            
            # Interpolate to desired r points
            T_interpolated = np.interp(r, r_cheb, T_cheb)
            
            return T_interpolated
            
        except Exception as e:
            self.debug_print(f"Chebyshev method error: {str(e)}")
            return np.ones_like(r) * self.T_boundary
    
    def chebyshev_diff_matrix(self, N):
        """
        Chebyshev differentiation matrix
        """
        if N == 0:
            return np.array([[0]])
        
        x = np.cos(np.pi * np.arange(N+1) / N)
        c = np.ones(N+1)
        c[0] = c[N] = 2
        c = c * (-1)**np.arange(N+1)
        
        X = np.tile(x, (N+1, 1))
        dX = X - X.T
        
        D = np.outer(c, 1/c) / (dX + np.eye(N+1))
        D = D - np.diag(np.sum(D, axis=1))
        
        return D
    
    def compare_methods(self, r_points, time_points, save_plots=True):
        """
        Analytical ve Chebyshev metodlarını karşılaştır
        """
        results = {
            'r': r_points,
            'times': time_points,
            'analytical': [],
            'chebyshev': [],
            'errors': []
        }
        
        print("\n" + "="*50)
        print("METHOD COMPARISON ANALYSIS")
        print("="*50)
        
        for t in time_points:
            print(f"\n--- Time t = {t} ---")
            
            # Analytical solution
            T_analytical = self.analytical_solution(r_points, t)
            
            # Chebyshev solution
            T_chebyshev = self.chebyshev_collocation(r_points, t)
            
            # Error analysis
            error = np.abs(T_analytical - T_chebyshev)
            max_error = np.max(error)
            rms_error = np.sqrt(np.mean(error**2))
            
            results['analytical'].append(T_analytical)
            results['chebyshev'].append(T_chebyshev)
            results['errors'].append(error)
            
            print(f"Max error: {max_error:.6f} °C")
            print(f"RMS error: {rms_error:.6f} °C")
            print(f"Analytical range: [{np.min(T_analytical):.1f}, {np.max(T_analytical):.1f}] °C")
            print(f"Chebyshev range: [{np.min(T_chebyshev):.1f}, {np.max(T_chebyshev):.1f}] °C")
        
        if save_plots:
            self.plot_comparison(results)
            self.plot_error_analysis(results)
        
        return results
    
    def plot_comparison(self, results):
        """
        Karşılaştırma grafiklerini çiz
        """
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        for i, t in enumerate(results['times']):
            row = i // 2
            col = i % 2
            
            ax = axes[row, col]
            
            # Plot both solutions
            ax.plot(results['r'], results['analytical'][i], 'b-', linewidth=2, 
                   label='Analytical', alpha=0.8)
            ax.plot(results['r'], results['chebyshev'][i], 'r--', linewidth=2, 
                   label='Chebyshev', alpha=0.8)
            
            ax.set_xlabel('r')
            ax.set_ylabel('Temperature (°C)')
            ax.set_title(f'Temperature Distribution at t = {t}s')
            ax.grid(True, alpha=0.3)
            ax.legend()
            ax.set_ylim(600, 1200)
        
        plt.tight_layout()
        plt.savefig('method_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def plot_error_analysis(self, results):
        """
        Hata analizi grafiği
        """
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # Error vs position
        for i, t in enumerate(results['times']):
            ax1.plot(results['r'], results['errors'][i], 
                    label=f't = {t}s', linewidth=2)
        
        ax1.set_xlabel('r')
        ax1.set_ylabel('|Error| (°C)')
        ax1.set_title('Absolute Error vs Position')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        ax1.set_yscale('log')
        
        # Error vs time
        max_errors = [np.max(err) for err in results['errors']]
        rms_errors = [np.sqrt(np.mean(err**2)) for err in results['errors']]
        
        ax2.plot(results['times'], max_errors, 'ro-', label='Max Error', linewidth=2)
        ax2.plot(results['times'], rms_errors, 'bs-', label='RMS Error', linewidth=2)
        ax2.set_xlabel('Time (s)')
        ax2.set_ylabel('Error (°C)')
        ax2.set_title('Error vs Time')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        ax2.set_yscale('log')
        
        plt.tight_layout()
        plt.savefig('error_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

# Test ve karşılaştırma kodu
if __name__ == "__main__":
    # Hollow cylinder problem setup
    solver = HollowCylinderThermal(a=1.0, b=1.5, kappa=50.0, 
                                  T_initial=1150.0, T_boundary=650.0)
    
    # Test points
    r_points = np.linspace(1.0, 1.5, 50)
    time_points = [0.0, 0.4, 0.8, 1.2]
    
    print("HOLLOW CYLINDER THERMAL ANALYSIS")
    print("Analytical vs Chebyshev Collocation Method Comparison")
    print(f"Inner radius: {solver.a}")
    print(f"Outer radius: {solver.b}")
    print(f"Thermal diffusivity: {solver.kappa}")
    print(f"Initial temperature: {solver.T_initial}°C")
    print(f"Boundary temperature: {solver.T_boundary}°C")
    
    # Run comparison
    results = solver.compare_methods(r_points, time_points)
    
    # Eigenvalue analysis
    print(f"\nEigenvalue Analysis:")
    print(f"Found {len(solver.eigenvalues)} eigenvalues")
    print(f"First 5 eigenvalues: {solver.eigenvalues[:5]}")
    
    # Convergence analysis
    print(f"\nConvergence Analysis:")
    print(f"Coefficients decay: {solver.coefficients[:5]}")